#!/bin/bash
#
# Space cleanup script for OpenWrt build
# This script helps free up disk space during the build process
#

set -e

echo "=== Disk Space Cleanup Script ==="
echo "Before cleanup:"
df -hT

# Clean APT cache and packages
echo "Cleaning APT cache..."
sudo apt-get clean
sudo apt-get autoremove -y --purge
sudo rm -rf /var/lib/apt/lists/*

# Clean temporary files
echo "Cleaning temporary files..."
sudo rm -rf /tmp/*
sudo rm -rf /var/tmp/*

# Clean Docker if available
if command -v docker &> /dev/null; then
    echo "Cleaning Docker..."
    sudo docker system prune -af --volumes || true
fi

# Clean snap packages (if any)
if command -v snap &> /dev/null; then
    echo "Cleaning snap packages..."
    sudo rm -rf /var/lib/snapd/cache/*
fi

# Clean logs
echo "Cleaning logs..."
sudo journalctl --vacuum-time=1d || true
sudo rm -rf /var/log/*.log
sudo rm -rf /var/log/*/*.log

# Clean build artifacts in current directory if we're in openwrt
if [ -d "build_dir" ]; then
    echo "Cleaning OpenWrt build artifacts..."
    rm -rf build_dir/host/*/doc
    rm -rf build_dir/host/*/man
    rm -rf build_dir/toolchain-*/info
    rm -rf staging_dir/host/share/doc
    rm -rf staging_dir/host/share/man
    rm -rf staging_dir/host/share/info
    find build_dir -name "*.o" -delete || true
    find build_dir -name "*.a" -delete || true
fi

echo "After cleanup:"
df -hT
echo "=== Cleanup completed ==="
